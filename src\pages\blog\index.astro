---
import BaseHead from '../../components/BaseHead.astro';
import Header from '../../components/Header.astro';
import Footer from '../../components/Footer.astro';
import { SITE_TITLE, SITE_DESCRIPTION } from '../../consts';
import { getCollection } from 'astro:content';
import FormattedDate from '../../components/FormattedDate.astro';
import { Image } from 'astro:assets';
import heroA from '../../assets/cooper-s-coding-notes.jpg';
import heroB from '../../assets/cooper-s-seo-summary.jpg';
import heroC from '../../assets/after-i-turn-off-alarm.webp';
import heroF from '../../assets/how-to-use-vpn-correctly.jpg';
import heroG from '../../assets/root-one-plus-8t-9008-oxgen-os.jpg';
import heroH from '../../assets/what-is-mcp.png';


const posts = (await getCollection('blog')).sort(
	(a, b) => b.data.pubDate.valueOf() - a.data.pubDate.valueOf(),
);

const { title, description, pubDate, updatedDate, heroImage } = Astro.props;
const heroMap = {
  'hero-a': heroA,
  'hero-b': heroB,
  'hero-c': heroC,
  'hero-f': heroF,
  "hero-g": heroG,
  "hero-h": heroH,
};

// 确保当前的 hero 图片与 frontmatter 中的 heroImage 对应
// The hero image mapping logic will be moved inside the map function
---

<!doctype html>
<html lang="en">
	<head>
		<!-- Google tag (gtag.js) -->
		<script async src="https://www.googletagmanager.com/gtag/js?id=G-DKECP77S2Y"></script>
		<script>
		window.dataLayer = window.dataLayer || [];
		function gtag(){dataLayer.push(arguments);}
		gtag('js', new Date());

		gtag('config', 'G-DKECP77S2Y');
		</script>
		<BaseHead title={SITE_TITLE} description={SITE_DESCRIPTION} />
		<style>
			main {
				width: 100%;
				max-width: 1200px;
				margin: 0 auto;
				padding: 2rem 1rem;
			}

			section {
				width: 100%;
				max-width: 960px;
				margin: 0 auto;
			}

			.blog-grid {
				display: grid;
				grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
				gap: 2rem;
				list-style: none;
				margin: 0;
				padding: 0;
			}

			.blog-card {
				background: var(--bg-color);
				border: 1px solid var(--border-color);
				border-radius: 0.75rem;
				overflow: hidden;
				transition: all 0.3s ease;
				box-shadow: var(--shadow-sm);
			}

			.blog-card:hover {
				transform: translateY(-4px);
				box-shadow: var(--shadow-lg);
				border-color: var(--accent);
			}

			.blog-card:first-child {
				grid-column: 1 / -1;
				display: flex;
				flex-direction: row;
				align-items: stretch;
				padding: 0;
				overflow: hidden;
			}

			.blog-card:first-child .card-image {
				flex: 1;
				order: 2;
			}

			.blog-card:first-child .card-content {
				flex: 1;
				order: 1;
				text-align: left;
				padding: 2rem;
				display: flex;
				flex-direction: column;
				justify-content: center;
			}

			.blog-card a {
				display: block;
				text-decoration: none;
				color: inherit;
				height: 100%;
			}

			.card-image {
				position: relative;
				overflow: hidden;
			}

			.card-image img {
				width: 100%;
				height: 200px;
				object-fit: cover;
				transition: transform 0.3s ease;
			}

			.blog-card:first-child .card-image img {
				height: 100%;
				min-height: 300px;
				border-radius: 0;
				object-fit: cover;
			}

			.blog-card:hover .card-image img {
				transform: scale(1.05);
			}

			.card-content {
				padding: 1.5rem;
			}

			/* Remove this rule as it's now handled above */

			.card-title {
				margin: 0 0 1rem 0;
				color: var(--text-color);
				line-height: var(--line-height-tight);
				font-size: var(--font-size-xl);
				font-weight: var(--font-weight-bold);
			}

			.blog-card:first-child .card-title {
				font-size: var(--font-size-4xl);
				margin-bottom: 1.5rem;
			}

			.card-date {
				margin: 0;
				color: var(--text-color-secondary);
				font-size: var(--font-size-sm);
				font-weight: var(--font-weight-normal);
			}

			.blog-card:hover .card-title {
				color: var(--accent);
			}

			.blog-card:hover .card-date {
				color: var(--text-color);
			}

			/* Desktop specific styles */
			@media (min-width: 769px) {
				.blog-card:first-child {
					min-height: 300px;
				}

				.blog-card:first-child .card-image {
					min-width: 50%;
				}
			}

			/* Mobile responsive */
			@media (max-width: 768px) {
				main {
					padding: 1.5rem 1rem;
				}

				.blog-grid {
					grid-template-columns: 1fr;
					gap: 1.5rem;
				}

				.blog-card:first-child {
					flex-direction: column;
					padding: 0;
				}

				.blog-card:first-child .card-image {
					order: 1;
					flex: none;
				}

				.blog-card:first-child .card-content {
					order: 2;
					text-align: center;
					padding: 1.5rem;
				}

				.blog-card:first-child .card-title {
					font-size: var(--font-size-3xl);
				}

				.card-image img {
					height: 180px;
				}

				.blog-card:first-child .card-image img {
					height: 200px;
				}
			}

			@media (max-width: 480px) {
				main {
					padding: 1rem 0.75rem;
				}

				.blog-grid {
					gap: 1rem;
				}

				.blog-card:first-child .card-content {
					padding: 1rem;
				}

				.blog-card:first-child .card-title {
					font-size: var(--font-size-2xl);
				}

				.card-content {
					padding: 1rem;
				}

				.card-image img {
					height: 160px;
				}
			}
		</style>
	</head>
	<body>
		<Header />
		<main>
			<section>
				<ul class="blog-grid">
					{
						posts.map((post) => {
							const imageSrc = heroMap[post.data.heroImage as keyof typeof heroMap];
							return (
								<li class="blog-card">
									<a href={`/blog/${post.id}/`}>
										{imageSrc && (
											<div class="card-image">
												<Image
												src={imageSrc}
												alt={post.data.title}
												width={720}
												height={360}
												loading="lazy"
												/>
											</div>
										)}
										<div class="card-content">
											<h4 class="card-title">{post.data.title}</h4>
											<p class="card-date">
												<FormattedDate date={post.data.pubDate} />
											</p>
										</div>
									</a>
								</li>
							);
						})
					}
				</ul>
			</section>
		</main>
		<Footer />
	</body>
</html>
