---
import type { CollectionEntry } from 'astro:content';
import BaseHead from '../components/BaseHead.astro';
import Header from '../components/Header.astro';
import Footer from '../components/Footer.astro';
import FormattedDate from '../components/FormattedDate.astro';
import Comment from '../components/Comment.astro';
import BackToTop from '../components/Backtotop.astro';
import { Image } from 'astro:assets';
import heroA from '../assets/cooper-s-coding-notes.jpg';
import heroB from '../assets/cooper-s-seo-summary.jpg';
import heroC from '../assets/after-i-turn-off-alarm.webp';
import heroD from '../assets/about-cooper.jpg';
import heroE from '../assets/tools.gif';
import heroF from '../assets/how-to-use-vpn-correctly.jpg';
import heroG from '../assets/root-one-plus-8t-9008-oxgen-os.jpg';
import heroH from '../assets/what-is-mcp.png';


type Props = CollectionEntry<'blog'>['data'];

const { title, description, pubDate, updatedDate, heroImage } = Astro.props;
const heroImageMap = {
  'hero-a': heroA,
  'hero-b': heroB,
  'hero-c': heroC,
  'hero-d': heroD,
  'hero-e': heroE,
  'hero-f': heroF,
  "hero-g": heroG,
  "hero-h": heroH,
};

// 确保当前的 hero 图片与 frontmatter 中的 heroImage 对应
const currentHero = heroImage?.toLowerCase(); // 动态获取 heroImage 并小写化
---

<html lang="en">
	<head>		
		<BaseHead title={title} description={description} />
		<style>
			main {
				width: 100%;
				max-width: 100%;
				margin: 0;
				padding: 0;
			}

			article {
				max-width: 1200px;
				margin: 0 auto;
			}

			.hero-image {
				width: 100%;
				margin-bottom: 2rem;
				position: relative;
				overflow: hidden;
			}

			.hero-image img {
				display: block;
				width: 100%;
				height: auto;
				max-height: 60vh;
				object-fit: cover;
				border-radius: 0;
			}

			.prose {
				max-width: 720px;
				margin: 0 auto;
				padding: 2rem 1.5rem;
				color: var(--text-color);
			}

			.title {
				margin-bottom: 2rem;
				padding: 2rem 0 1rem;
				text-align: center;
				border-bottom: 1px solid var(--border-color-light);
			}

			.title h1 {
				margin: 0 0 1rem 0;
				color: var(--text-color);
				font-size: var(--font-size-4xl);
				line-height: var(--line-height-tight);
				font-weight: var(--font-weight-bold);
			}

			.date {
				margin-bottom: 0.5rem;
				color: var(--text-color-secondary);
				font-size: var(--font-size-sm);
				font-weight: var(--font-weight-normal);
			}

			.last-updated-on {
				font-style: italic;
				color: var(--text-color-muted);
				font-size: var(--font-size-sm);
				margin-top: 0.5rem;
			}

			/* Article content styling */
			.prose h2 {
				margin-top: 3rem;
				margin-bottom: 1rem;
				padding-bottom: 0.5rem;
				border-bottom: 2px solid var(--border-color-light);
			}

			.prose h3 {
				margin-top: 2rem;
				margin-bottom: 1rem;
			}

			.prose p {
				margin-bottom: 1.5rem;
				line-height: var(--line-height-relaxed);
			}

			.prose img {
				margin: 2rem 0;
				border-radius: 0.5rem;
				box-shadow: var(--shadow-md);
			}

			.prose pre {
				margin: 2rem 0;
				border-radius: 0.5rem;
				overflow-x: auto;
			}

			.prose blockquote {
				margin: 2rem 0;
				padding: 1.5rem;
				border-left: 4px solid var(--accent);
				background: var(--bg-secondary);
				border-radius: 0 0.5rem 0.5rem 0;
			}

			/* Mobile responsive */
			@media (max-width: 768px) {
				.prose {
					padding: 1.5rem 1rem;
				}

				.title {
					padding: 1.5rem 0 1rem;
				}

				.title h1 {
					font-size: var(--font-size-3xl);
				}

				.hero-image img {
					max-height: 40vh;
				}
			}

			@media (max-width: 480px) {
				.prose {
					padding: 1rem 0.75rem;
				}

				.title h1 {
					font-size: var(--font-size-2xl);
				}

				.hero-image img {
					max-height: 30vh;
				}
			}
		</style>

		

		<script type="text/javascript">
			(function(c,l,a,r,i,t,y){
				c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
				t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
				y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
			})(window, document, "clarity", "script", "r4ew4dl716");
		</script>
        <!-- Google tag (gtag.js) -->
		<script async src="https://www.googletagmanager.com/gtag/js?id=G-DKECP77S2Y"></script>
		<script>
		window.dataLayer = window.dataLayer || [];
		function gtag(){dataLayer.push(arguments);}
		gtag('js', new Date());

		gtag('config', 'G-DKECP77S2Y');
		</script>

	</head>
	<body>
		<Header />
		<main>
			<article>
				<div class="hero-image">
					<Image
					src={heroImageMap[currentHero as keyof typeof heroImageMap]}
					alt="title"
					width={1020}
					height={510}
					loading="eager"
				  />				  
				</div>
				<div class="prose">
					<div class="title">
						<div class="date">
							<FormattedDate date={pubDate} />
							{
								updatedDate && (
									<div class="last-updated-on">
										Last updated on <FormattedDate date={updatedDate} />
									</div>
								)
							}
						</div>
						<h1>{title}</h1>
						<hr />
					</div>
					<slot />
				</div>
			</article>
			<BackToTop />
			<Comment />
		</main>
		<Footer />
	</body>
</html>
