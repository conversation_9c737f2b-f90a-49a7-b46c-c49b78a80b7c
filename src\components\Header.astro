---
import HeaderLink from './HeaderLink.astro';
import ThemeToggle from './ThemeToggle.astro';
import { SITE_TITLE } from '../consts';
---

<header>
	<nav>
		<h2><a href="/">{SITE_TITLE}</a></h2>
		<div class="internal-links">
			<HeaderLink href="/" title="Howdy">😎</HeaderLink>
			<HeaderLink href="/blog" title="Cooper's Log">📝</HeaderLink>
			<HeaderLink href="/about" title="<PERSON> Cooper">👤</HeaderLink>
			<HeaderLink href="/tools" title="<PERSON>'s Tools">🧰</HeaderLink>
		</div>
		<div class="header-right">
			<ThemeToggle />
			<div class="social-links">
				<a href="https://x.com/Leonozzzzz" target="_blank" title="Follow <PERSON><PERSON>Cooper on X">
					<span class="sr-only">Follow <PERSON><PERSON> on X</span>
					<svg viewBox="0 0 16 16" aria-hidden="true" width="32" height="32"
						><path
							fill="currentColor"
							d="M5.026 15c6.038 0 9.341-5.003 9.341-9.334 0-.14 0-.282-.006-.422A6.685 6.685 0 0 0 16 3.542a6.658 6.658 0 0 1-1.889.518 3.301 3.301 0 0 0 1.447-1.817 6.533 6.533 0 0 1-2.087.793A3.286 3.286 0 0 0 7.875 6.03a9.325 9.325 0 0 1-6.767-3.429 3.289 3.289 0 0 0 1.018 4.382A3.323 3.323 0 0 1 .64 6.575v.045a3.288 3.288 0 0 0 2.632 3.218 3.203 3.203 0 0 1-.865.115 3.23 3.23 0 0 1-.614-.057 3.283 3.283 0 0 0 3.067 2.277A6.588 6.588 0 0 1 .78 13.58a6.32 6.32 0 0 1-.78-.045A9.344 9.344 0 0 0 5.026 15z"
						></path></svg
					>
				</a>
				<a href="https://github.com/skywalker23241" target="_blank" title="Go to J.Cooper's GitHub">
					<span class="sr-only">Go to J.Cooper's GitHub</span>
					<svg viewBox="0 0 16 16" aria-hidden="true" width="32" height="32"
						><path
							fill="currentColor"
							d="M8 0C3.58 0 0 3.58 0 8c0 3.54 2.29 6.53 5.47 7.59.4.07.55-.17.55-.38 0-.19-.01-.82-.01-1.49-2.01.37-2.53-.49-2.69-.94-.09-.23-.48-.94-.82-1.13-.28-.15-.68-.52-.01-.53.63-.01 1.08.58 1.23.82.72 1.21 1.87.87 2.33.66.07-.52.28-.87.51-1.07-1.78-.2-3.64-.89-3.64-3.95 0-.87.31-1.59.82-2.15-.08-.2-.36-1.02.08-2.12 0 0 .67-.21 2.2.82.64-.18 1.32-.27 2-.27.68 0 1.36.09 2 .27 1.53-1.04 2.2-.82 2.2-.82.44 1.1.16 1.92.08 2.12.51.56.82 1.27.82 2.15 0 3.07-1.87 3.75-3.65 3.95.29.25.54.73.54 1.48 0 1.07-.01 1.93-.01 2.2 0 .21.15.46.55.38A8.012 8.012 0 0 0 16 8c0-4.42-3.58-8-8-8z"
						></path></svg
					>
				</a>
				<a href="/rss.xml" target="_blank" aria-label="Subscribe to RSS Feed" title="Subscribe to RSS Feed">
					<span class="sr-only">Subscribe to RSS Feed</span>
					<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="currentColor" class="bi bi-rss" viewBox="0 0 16 16">
						<path d="M14 1a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1H2a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1zM2 0a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2z"/>
						<path d="M5.5 12a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0m-3-8.5a1 1 0 0 1 1-1c5.523 0 10 4.477 10 10a1 1 0 1 1-2 0 8 8 0 0 0-8-8 1 1 0 0 1-1-1m0 4a1 1 0 0 1 1-1 6 6 0 0 1 6 6 1 1 0 1 1-2 0 4 4 0 0 0-4-4 1 1 0 0 1-1-1"/>
					  </svg>
				</a>		
			</div>
		</div>
	</nav>
</header>
<style>
	header {
		position: sticky;
		top: 0;
		z-index: 100;
		margin: 0;
		padding: 0;
		background: var(--header-bg);
		backdrop-filter: blur(10px);
		box-shadow: var(--header-shadow);
		border-bottom: 1px solid var(--border-color-light);
	}

	nav {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 1rem 1.5rem;
		max-width: 1200px;
		margin: 0 auto;
	}

	h2 {
		margin: 0;
		font-size: var(--font-size-xl);
		font-weight: var(--font-weight-bold);
	}

	h2 a {
		color: var(--text-color);
		text-decoration: none;
		transition: color 0.2s ease;
	}

	h2 a:hover {
		color: var(--accent);
	}

	.internal-links {
		display: flex;
		align-items: center;
		gap: 0.5rem;
	}

	.internal-links a {
		padding: 0.75rem 1rem;
		color: var(--text-color-secondary);
		border-bottom: 3px solid transparent;
		text-decoration: none;
		font-size: var(--font-size-sm);
		font-weight: var(--font-weight-normal);
		transition: all 0.2s ease;
		border-radius: 0.375rem 0.375rem 0 0;
	}

	.internal-links a:hover {
		color: var(--text-color);
		background-color: var(--bg-secondary);
		border-bottom-color: var(--accent);
	}

	.internal-links a.active {
		color: var(--accent);
		border-bottom-color: var(--accent);
		background-color: var(--bg-secondary);
	}

	.header-right {
		display: flex;
		align-items: center;
		gap: 1rem;
	}

	.social-links {
		display: flex;
		align-items: center;
		gap: 0.75rem;
	}

	.social-links a {
		display: flex;
		align-items: center;
		justify-content: center;
		width: 40px;
		height: 40px;
		color: var(--icon-color);
		text-decoration: none;
		border-radius: 50%;
		transition: all 0.2s ease;
	}

	.social-links a:hover {
		color: var(--icon-hover-color);
		background-color: var(--toc-hover);
		transform: scale(1.1);
	}

	.social-links svg {
		width: 20px;
		height: 20px;
	}

	/* Mobile Responsive */
	@media (max-width: 768px) {
		nav {
			padding: 1rem;
		}

		.internal-links {
			gap: 0.25rem;
		}

		.internal-links a {
			padding: 0.5rem 0.75rem;
			font-size: var(--font-size-xs);
		}

		.social-links {
			display: none;
		}

		h2 {
			font-size: var(--font-size-lg);
		}
	}

	@media (max-width: 480px) {
		nav {
			flex-direction: column;
			gap: 1rem;
			padding: 1rem 0.75rem;
		}

		.internal-links {
			order: 2;
		}

		.header-right {
			order: 3;
		}

		h2 {
			order: 1;
		}
	}
</style>
