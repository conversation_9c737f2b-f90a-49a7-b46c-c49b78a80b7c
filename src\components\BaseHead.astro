---
// Import the global.css file here so that it is included on
// all pages through the use of the <BaseHead /> component.
import '../styles/global.css';
import '../styles/components.css';
import { SITE_TITLE, SITE_DESCRIPTION } from '../consts';

interface Props {
	title: string;
	description: string;
	image?: string;
	type?: 'website' | 'article';
	publishedTime?: string;
	modifiedTime?: string;
	author?: string;
}

const canonicalURL = new URL(Astro.url.pathname, Astro.site);

const {
	title,
	description,
	image = '/favicon.svg',
	type = 'website',
	publishedTime,
	modifiedTime,
	author = '<PERSON><PERSON>'
} = Astro.props;

// Construct full title
const fullTitle = title === SITE_TITLE ? title : `${title} | ${SITE_TITLE}`;
---

<!-- Global Metadata -->
<meta charset="utf-8" />
<meta name="viewport" content="width=device-width,initial-scale=1" />
<link rel="icon" type="image/svg+xml" href="/favicon.svg" />
<link rel="sitemap" href="/sitemap-index.xml" />
<link
	rel="alternate"
	type="application/rss+xml"
	title={SITE_TITLE}
	href={new URL('rss.xml', Astro.site)}
/>
<meta name="generator" content={Astro.generator} />

<!-- Performance optimizations -->
<link rel="dns-prefetch" href="//fonts.googleapis.com" />
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />

<!-- Font preloads -->
<link rel="preload" href="/fonts/MapleMono-Regular.woff2" as="font" type="font/woff2" crossorigin />
<link rel="preload" href="/fonts/MapleMono-Bold.woff2" as="font" type="font/woff2" crossorigin />

<!-- Canonical URL -->
<link rel="canonical" href={canonicalURL} />

<!-- Primary Meta Tags -->
<title>{fullTitle}</title>
<meta name="title" content={fullTitle} />
<meta name="description" content={description} />
<meta name="author" content={author} />
<meta name="robots" content="index, follow" />
<meta name="language" content="English" />

<!-- Theme color for mobile browsers -->
<meta name="theme-color" content="#2337ff" />
<meta name="msapplication-TileColor" content="#2337ff" />

<!-- Open Graph / Facebook -->
<meta property="og:type" content={type} />
<meta property="og:url" content={Astro.url} />
<meta property="og:title" content={fullTitle} />
<meta property="og:description" content={description} />
<meta property="og:image" content={new URL(image, Astro.url)} />
<meta property="og:site_name" content={SITE_TITLE} />
<meta property="og:locale" content="en_US" />
{publishedTime && <meta property="article:published_time" content={publishedTime} />}
{modifiedTime && <meta property="article:modified_time" content={modifiedTime} />}
{author && <meta property="article:author" content={author} />}

<!-- Twitter -->
<meta property="twitter:card" content="summary_large_image" />
<meta property="twitter:url" content={Astro.url} />
<meta property="twitter:title" content={fullTitle} />
<meta property="twitter:description" content={description} />
<meta property="twitter:image" content={new URL(image, Astro.url)} />
<meta property="twitter:creator" content="@Leonozzzzz" />
<meta property="twitter:site" content="@Leonozzzzz" />

<!-- Additional SEO -->
<meta name="format-detection" content="telephone=no" />
<meta name="apple-mobile-web-app-capable" content="yes" />
<meta name="apple-mobile-web-app-status-bar-style" content="default" />

<!-- Structured Data -->
<script type="application/ld+json" is:inline>
{
	"@context": "https://schema.org",
	"@type": "WebSite",
	"name": "{fullTitle}",
	"description": "{description}",
	"url": "{Astro.url}",
	"author": {
		"@type": "Person",
		"name": "{author}"
	},
	"publisher": {
		"@type": "Organization",
		"name": "{SITE_TITLE}",
		"url": "{Astro.site}"
	}
}
</script>
