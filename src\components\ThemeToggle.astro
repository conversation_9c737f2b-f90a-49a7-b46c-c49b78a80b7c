<button id="theme-toggle" class="theme-toggle" aria-label="切换主题">
	<svg class="sun-icon" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
		<circle cx="12" cy="12" r="5"></circle>
		<line x1="12" y1="1" x2="12" y2="3"></line>
		<line x1="12" y1="21" x2="12" y2="23"></line>
		<line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
		<line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
		<line x1="1" y1="12" x2="3" y2="12"></line>
		<line x1="21" y1="12" x2="23" y2="12"></line>
		<line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
		<line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
	</svg>
	<svg class="moon-icon" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
		<path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"></path>
	</svg>
</button>

<script>
	// 主题初始化函数
	function initializeTheme() {
		const savedTheme = localStorage.getItem('theme');
		const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
		const theme = savedTheme || (prefersDark ? 'dark' : 'light');

		document.documentElement.setAttribute('data-theme', theme);
		return theme;
	}

	// 主题切换函数
	function toggleTheme() {
		const currentTheme = document.documentElement.getAttribute('data-theme');
		const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

		// 添加过渡类
		document.documentElement.classList.add('theme-transition');

		// 更新主题
		document.documentElement.setAttribute('data-theme', newTheme);
		localStorage.setItem('theme', newTheme);

		// 移除过渡类
		setTimeout(() => {
			document.documentElement.classList.remove('theme-transition');
		}, 300);
	}

	// 监听系统主题变化
	function watchSystemTheme() {
		const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
		mediaQuery.addEventListener('change', (e) => {
			if (!localStorage.getItem('theme')) {
				const theme = e.matches ? 'dark' : 'light';
				document.documentElement.setAttribute('data-theme', theme);
			}
		});
	}

	// 初始化
	document.addEventListener('DOMContentLoaded', () => {
		initializeTheme();
		watchSystemTheme();

		const themeToggle = document.getElementById('theme-toggle');
		if (themeToggle) {
			themeToggle.addEventListener('click', toggleTheme);
		}
	});

	// 立即执行主题初始化（避免闪烁）
	initializeTheme();
</script>

<style>
	/* Theme transition for smooth switching */
	.theme-transition,
	.theme-transition *,
	.theme-transition *:before,
	.theme-transition *:after {
		transition: all 0.3s ease !important;
		transition-delay: 0 !important;
	}

	.theme-toggle {
		background: none;
		border: none;
		padding: 0.5rem;
		cursor: pointer;
		color: var(--icon-color);
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 50%;
		transition: all 0.3s ease;
		position: relative;
		width: 40px;
		height: 40px;
	}

	.theme-toggle:hover {
		background-color: var(--toc-hover);
		color: var(--icon-hover-color);
		transform: scale(1.1);
	}

	.theme-toggle:focus {
		outline: 2px solid var(--accent);
		outline-offset: 2px;
	}

	.sun-icon,
	.moon-icon {
		width: 24px;
		height: 24px;
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		transition: all 0.3s ease;
		pointer-events: none;
	}

	.moon-icon {
		opacity: 0;
		visibility: hidden;
		transform: translate(-50%, -50%) rotate(180deg) scale(0.8);
	}

	.sun-icon {
		opacity: 1;
		visibility: visible;
		transform: translate(-50%, -50%) rotate(0deg) scale(1);
	}

	[data-theme="dark"] .sun-icon {
		opacity: 0;
		visibility: hidden;
		transform: translate(-50%, -50%) rotate(-180deg) scale(0.8);
	}

	[data-theme="dark"] .moon-icon {
		opacity: 1;
		visibility: visible;
		transform: translate(-50%, -50%) rotate(0deg) scale(1);
	}
</style>